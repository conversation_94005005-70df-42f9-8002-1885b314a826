import React, { useState } from "react";
import { Image, Button, Space, Typography, Modal, Tooltip } from "antd";
import {
  DownloadOutlined,
  EyeOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileImageOutlined,
  PlayCircleOutlined,
  SoundOutlined,
} from "@ant-design/icons";
import { FileDownloadService } from "@app/services/fileDownload.service";
import type { ChatAttachment } from "@app/types/chat";

const { Text } = Typography;

interface ChatAttachmentsProps {
  attachments: any[]; // Estructura de archivos de Strapi
}

export const ChatAttachments: React.FC<ChatAttachmentsProps> = ({
  attachments,
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [fileViewerVisible, setFileViewerVisible] = useState(false);
  const [currentFile, setCurrentFile] = useState<any>(null);

  // Obtener icono según el tipo de archivo
  const getFileIcon = (attachment: ChatAttachment) => {
    const { mimeType, type } = attachment;

    if (type === "image")
      return <FileImageOutlined style={{ color: "#52c41a" }} />;
    if (type === "video")
      return <PlayCircleOutlined style={{ color: "#1890ff" }} />;
    if (type === "audio") return <SoundOutlined style={{ color: "#722ed1" }} />;

    // Verificar que mimeType existe antes de usar includes
    if (mimeType && mimeType.includes("pdf"))
      return <FilePdfOutlined style={{ color: "#ff4d4f" }} />;
    if (
      mimeType &&
      (mimeType.includes("word") || mimeType.includes("document"))
    ) {
      return <FileWordOutlined style={{ color: "#1890ff" }} />;
    }

    return <FileOutlined style={{ color: "#8c8c8c" }} />;
  };

  // Formatear tamaño de archivo (Strapi devuelve en KB)
  const formatFileSize = (sizeInKB: number) => {
    // Convertir de KB a bytes para usar el servicio
    const sizeInBytes = sizeInKB * 1024;
    return FileDownloadService.formatFileSize(sizeInBytes);
  };

  // Manejar apertura de archivo en modal
  const handleOpenFile = (attachment: any) => {
    setCurrentFile(attachment);
    setFileViewerVisible(true);
  };

  // Manejar descarga forzada
  const handleDownload = async (attachment: any) => {
    try {
      await FileDownloadService.downloadFile(attachment.url, {
        filename: attachment.name,
        showProgress: true,
      });
    } catch (error) {
      console.error("Error descargando archivo:", error);
    }
  };

  // Detectar tipo de archivo basándose en la estructura de Strapi
  const getFileType = (attachment: any): string => {
    console.log("🔍 Detectando tipo de archivo:", {
      name: attachment.name,
      type: attachment.type,
      mimeType: attachment.mimeType,
      mime: attachment.mime, // Campo de Strapi
      strapiData: attachment,
    });

    // Obtener el mimeType correcto (puede estar en 'mime' o 'mimeType')
    const mimeType = attachment.mime || attachment.mimeType;

    // Si el tipo ya está definido y es correcto, usarlo
    if (attachment.type && attachment.type !== "document") {
      console.log("✅ Usando tipo definido:", attachment.type);
      return attachment.type;
    }

    // Detectar basándose en mimeType de Strapi
    if (mimeType) {
      if (mimeType.startsWith("image/")) {
        console.log("✅ Detectado como imagen por mime:", mimeType);
        return "image";
      }
      if (mimeType.startsWith("video/")) {
        console.log("✅ Detectado como video por mime:", mimeType);
        return "video";
      }
      if (mimeType.startsWith("audio/")) {
        console.log("✅ Detectado como audio por mime:", mimeType);
        return "audio";
      }
    }

    // Por defecto, documento
    console.log("⚠️ Usando tipo por defecto: document");
    return "document";
  };

  // Manejar vista previa de imagen
  const handlePreview = (attachment: any) => {
    setPreviewImage(attachment.url);
    setPreviewVisible(true);
  };

  if (!attachments || attachments.length === 0) {
    return null;
  }

  return (
    <div style={{ marginTop: "8px" }}>
      <Space direction="vertical" size="small" style={{ width: "100%" }}>
        {attachments.map((attachment) => {
          const isImage = getFileType(attachment) === "image";

          return (
            <div
              key={attachment.id}
              style={{
                border: "1px solid #f0f0f0",
                borderRadius: "6px",
                padding: "8px",
                backgroundColor: "#fafafa",
                maxWidth: "300px",
              }}
            >
              {isImage ? (
                // Vista previa de imagen
                <div>
                  <Image
                    src={attachment.url}
                    alt={attachment.name}
                    style={{
                      maxWidth: "100%",
                      maxHeight: "200px",
                      borderRadius: "4px",
                    }}
                    preview={{
                      mask: (
                        <div style={{ color: "white", textAlign: "center" }}>
                          <EyeOutlined style={{ fontSize: "20px" }} />
                          <div>Ver imagen</div>
                        </div>
                      ),
                    }}
                  />
                  <div
                    style={{
                      marginTop: "4px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div>
                      <Text style={{ fontSize: "12px", fontWeight: 500 }}>
                        {attachment.name}
                      </Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: "11px" }}>
                        {formatFileSize(attachment.size)}
                      </Text>
                    </div>
                    <Space>
                      <Tooltip title="Ver archivo">
                        <Button
                          type="text"
                          size="small"
                          icon={<EyeOutlined />}
                          onClick={() => handleOpenFile(attachment)}
                        />
                      </Tooltip>
                      <Tooltip title="Descargar">
                        <Button
                          type="text"
                          size="small"
                          icon={<DownloadOutlined />}
                          onClick={() => handleDownload(attachment)}
                        />
                      </Tooltip>
                    </Space>
                  </div>
                </div>
              ) : (
                // Archivo no imagen
                <div
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                >
                  <div style={{ fontSize: "24px" }}>
                    {getFileIcon(attachment)}
                  </div>
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <Text
                      style={{
                        fontSize: "13px",
                        fontWeight: 500,
                        display: "block",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                    >
                      {attachment.name}
                    </Text>
                    <Text type="secondary" style={{ fontSize: "11px" }}>
                      {formatFileSize(attachment.size)}
                    </Text>
                  </div>
                  <Space>
                    {getFileType(attachment) === "image" ? (
                      <Tooltip title="Vista previa">
                        <Button
                          type="text"
                          size="small"
                          icon={<EyeOutlined />}
                          onClick={() => handlePreview(attachment)}
                        />
                      </Tooltip>
                    ) : (
                      <Tooltip title="Ver archivo">
                        <Button
                          type="text"
                          size="small"
                          icon={<EyeOutlined />}
                          onClick={() => handleOpenFile(attachment)}
                        />
                      </Tooltip>
                    )}
                    <Tooltip title="Descargar">
                      <Button
                        type="text"
                        size="small"
                        icon={<DownloadOutlined />}
                        onClick={() => handleDownload(attachment)}
                      />
                    </Tooltip>
                  </Space>
                </div>
              )}
            </div>
          );
        })}
      </Space>

      {/* Modal de vista previa de imágenes */}
      <Modal
        open={previewVisible}
        title="Vista previa"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width="80%"
        style={{ maxWidth: "800px" }}
      >
        <img
          src={previewImage}
          alt="Vista previa"
          style={{
            width: "100%",
            height: "auto",
            maxHeight: "70vh",
            objectFit: "contain",
          }}
        />
      </Modal>

      {/* Modal de visualización de archivos */}
      <Modal
        open={fileViewerVisible}
        title={currentFile?.name || "Visualizar archivo"}
        footer={[
          <Button
            key="download"
            icon={<DownloadOutlined />}
            onClick={() => currentFile && handleDownload(currentFile)}
          >
            Descargar
          </Button>,
          <Button key="close" onClick={() => setFileViewerVisible(false)}>
            Cerrar
          </Button>,
        ]}
        onCancel={() => setFileViewerVisible(false)}
        width="90%"
        style={{ maxWidth: "1000px" }}
      >
        {currentFile && (
          <div style={{ textAlign: "center" }}>
            {getFileType(currentFile) === "image" ? (
              <img
                src={currentFile.url}
                alt={currentFile.name}
                style={{
                  maxWidth: "100%",
                  maxHeight: "70vh",
                  objectFit: "contain",
                }}
              />
            ) : getFileType(currentFile) === "video" ? (
              <video
                src={currentFile.url}
                controls
                style={{
                  maxWidth: "100%",
                  maxHeight: "70vh",
                }}
              >
                Tu navegador no soporta el elemento de video.
              </video>
            ) : getFileType(currentFile) === "audio" ? (
              <audio src={currentFile.url} controls style={{ width: "100%" }}>
                Tu navegador no soporta el elemento de audio.
              </audio>
            ) : (
              <div style={{ padding: "40px", textAlign: "center" }}>
                <div style={{ fontSize: "48px", marginBottom: "16px" }}>
                  {getFileIcon(currentFile)}
                </div>
                <Text
                  strong
                  style={{
                    fontSize: "16px",
                    display: "block",
                    marginBottom: "8px",
                  }}
                >
                  {currentFile.name}
                </Text>
                <Text
                  type="secondary"
                  style={{
                    fontSize: "14px",
                    display: "block",
                    marginBottom: "16px",
                  }}
                >
                  Tamaño: {formatFileSize(currentFile.size)}
                </Text>
                <Text type="secondary">
                  Este tipo de archivo no se puede previsualizar. Puedes
                  descargarlo para abrirlo en tu aplicación predeterminada.
                </Text>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};
