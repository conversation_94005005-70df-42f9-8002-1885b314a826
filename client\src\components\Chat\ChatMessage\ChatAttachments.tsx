import React, { useState } from "react";
import { Image, Button, Space, Typography, Modal, Tooltip } from "antd";
import {
  DownloadOutlined,
  EyeOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileImageOutlined,
  PlayCircleOutlined,
  SoundOutlined,
} from "@ant-design/icons";
import { FileDownloadService } from "@app/services/fileDownload.service";
import type { ChatAttachment } from "@app/types/chat";

const { Text } = Typography;

interface ChatAttachmentsProps {
  attachments: ChatAttachment[];
}

export const ChatAttachments: React.FC<ChatAttachmentsProps> = ({
  attachments,
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState("");

  // Obtener icono según el tipo de archivo
  const getFileIcon = (attachment: ChatAttachment) => {
    const { mimeType, type } = attachment;

    if (type === "image")
      return <FileImageOutlined style={{ color: "#52c41a" }} />;
    if (type === "video")
      return <PlayCircleOutlined style={{ color: "#1890ff" }} />;
    if (type === "audio") return <SoundOutlined style={{ color: "#722ed1" }} />;

    // Verificar que mimeType existe antes de usar includes
    if (mimeType && mimeType.includes("pdf"))
      return <FilePdfOutlined style={{ color: "#ff4d4f" }} />;
    if (
      mimeType &&
      (mimeType.includes("word") || mimeType.includes("document"))
    ) {
      return <FileWordOutlined style={{ color: "#1890ff" }} />;
    }

    return <FileOutlined style={{ color: "#8c8c8c" }} />;
  };

  // Formatear tamaño de archivo usando el servicio
  const formatFileSize = (bytes: number) => {
    return FileDownloadService.formatFileSize(bytes);
  };

  // Manejar apertura de archivo en nueva pestaña
  const handleOpenFile = async (attachment: ChatAttachment) => {
    try {
      await FileDownloadService.openFile(attachment.url, {
        showProgress: true,
      });
    } catch (error) {
      console.error("Error abriendo archivo:", error);
    }
  };

  // Manejar descarga forzada
  const handleDownload = async (attachment: ChatAttachment) => {
    try {
      await FileDownloadService.downloadFile(attachment.url, {
        filename: attachment.name,
        showProgress: true,
      });
    } catch (error) {
      console.error("Error descargando archivo:", error);
    }
  };

  // Manejar vista previa de imagen
  const handlePreview = (attachment: ChatAttachment) => {
    setPreviewImage(attachment.url);
    setPreviewVisible(true);
  };

  if (!attachments || attachments.length === 0) {
    return null;
  }

  return (
    <div style={{ marginTop: "8px" }}>
      <Space direction="vertical" size="small" style={{ width: "100%" }}>
        {attachments.map((attachment) => {
          const isImage = attachment.type === "image";

          return (
            <div
              key={attachment.id}
              style={{
                border: "1px solid #f0f0f0",
                borderRadius: "6px",
                padding: "8px",
                backgroundColor: "#fafafa",
                maxWidth: "300px",
              }}
            >
              {isImage ? (
                // Vista previa de imagen
                <div>
                  <Image
                    src={attachment.url}
                    alt={attachment.name}
                    style={{
                      maxWidth: "100%",
                      maxHeight: "200px",
                      borderRadius: "4px",
                    }}
                    preview={{
                      mask: (
                        <div style={{ color: "white", textAlign: "center" }}>
                          <EyeOutlined style={{ fontSize: "20px" }} />
                          <div>Ver imagen</div>
                        </div>
                      ),
                    }}
                  />
                  <div
                    style={{
                      marginTop: "4px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div>
                      <Text style={{ fontSize: "12px", fontWeight: 500 }}>
                        {attachment.name}
                      </Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: "11px" }}>
                        {formatFileSize(attachment.size)}
                      </Text>
                    </div>
                    <Space>
                      <Tooltip title="Ver archivo">
                        <Button
                          type="text"
                          size="small"
                          icon={<EyeOutlined />}
                          onClick={() => handleOpenFile(attachment)}
                        />
                      </Tooltip>
                      <Tooltip title="Descargar">
                        <Button
                          type="text"
                          size="small"
                          icon={<DownloadOutlined />}
                          onClick={() => handleDownload(attachment)}
                        />
                      </Tooltip>
                    </Space>
                  </div>
                </div>
              ) : (
                // Archivo no imagen
                <div
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                >
                  <div style={{ fontSize: "24px" }}>
                    {getFileIcon(attachment)}
                  </div>
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <Text
                      style={{
                        fontSize: "13px",
                        fontWeight: 500,
                        display: "block",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                    >
                      {attachment.name}
                    </Text>
                    <Text type="secondary" style={{ fontSize: "11px" }}>
                      {formatFileSize(attachment.size)}
                    </Text>
                  </div>
                  <Space>
                    {attachment.type === "image" ? (
                      <Tooltip title="Vista previa">
                        <Button
                          type="text"
                          size="small"
                          icon={<EyeOutlined />}
                          onClick={() => handlePreview(attachment)}
                        />
                      </Tooltip>
                    ) : (
                      <Tooltip title="Ver archivo">
                        <Button
                          type="text"
                          size="small"
                          icon={<EyeOutlined />}
                          onClick={() => handleOpenFile(attachment)}
                        />
                      </Tooltip>
                    )}
                    <Tooltip title="Descargar">
                      <Button
                        type="text"
                        size="small"
                        icon={<DownloadOutlined />}
                        onClick={() => handleDownload(attachment)}
                      />
                    </Tooltip>
                  </Space>
                </div>
              )}
            </div>
          );
        })}
      </Space>

      {/* Modal de vista previa */}
      <Modal
        open={previewVisible}
        title="Vista previa"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width="80%"
        style={{ maxWidth: "800px" }}
      >
        <img
          src={previewImage}
          alt="Vista previa"
          style={{
            width: "100%",
            height: "auto",
            maxHeight: "70vh",
            objectFit: "contain",
          }}
        />
      </Modal>
    </div>
  );
};
