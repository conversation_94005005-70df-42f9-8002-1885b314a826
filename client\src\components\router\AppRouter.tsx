import ForgotPasswordPage from "@app/pages/ForgotPasswordPage";
import LockPage from "@app/pages/LockPage";
import LoginPage from "@app/pages/LoginPage";
import NewPasswordPage from "@app/pages/NewPasswordPage";
import SecurityCodePage from "@app/pages/SecurityCodePage";
import SignUpPage from "@app/pages/SignUpPage";
import { lazy } from "react";
import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";

// no lazy loading for auth pages to avoid flickering
const AuthLayout = lazy(
  () => import("@app/components/layouts/AuthLayout/AuthLayout"),
);

import UserRoleDisplay from "@app/components/common/UserRoleDisplay";
import MainLayout from "@app/components/layouts/main/MainLayout/MainLayout";
import ProfileLayout from "@app/components/profile/ProfileLayout";
import { withLoading } from "@app/hocs/withLoading.hoc";
import BudgetAdminPage from "@app/pages/AdminPages/budget-admin-page/budget-admin-page";
import BussinessAdminPage from "@app/pages/AdminPages/bussiness-admin-page/bussiness-admin-page";
import ComplaintsAdminPage from "@app/pages/AdminPages/complaints-admin-page/ComplaintsAdminPage";
import HousesAdminPage from "@app/pages/AdminPages/HousesAdminPage/HousesAdminPage";
import PaymentAdminPage from "@app/pages/AdminPages/PaymentAdminPage/payment-admin-page";
import PetsAdminPage from "@app/pages/AdminPages/PetsAdminPage";
import ReservationsAdminPage from "@app/pages/AdminPages/reservations-admin-page/reservations-admin-page";
import UsersAdminPage from "@app/pages/AdminPages/UsersAdminPage/UsersAdminPage";
import { ConsentManagementPage } from "@app/pages/AdminPages/ConsentManagementPage";
import ReservationRequestsDetailPage from "@app/pages/CommonAreasPage/components/ReservationDetails/ReservationDetails";
import ReservationRequestsPage from "@app/pages/CommonAreasPage/components/ReservationRequests/ReservationRequests";
import ComplaintsPage from "@app/pages/complaints/ComplaintsPage";
import DashboardPage from "@app/pages/DashboardPages/DashboardPage";
import DataUsersTablesPage from "@app/pages/DataUsersTablesPage/DataTablesPage";
import OperativeStaffPage from "@app/pages/OperativeStaffPage/OperativeStaffPage";
import PaymentsPage from "@app/pages/payments-info-page/payments-info-page";
import PoolAccessControlPage from "@app/pages/pool-access-control-page/pool-access-control-page";
import { ErrorBoundary } from "../common/ErrorBoundary/ErrorBoundary";
import RequireAuth from "./RequireAuth";

import { WarningsProvider } from "@app/pages/AdminPages/warnings-admin-page/context/WarningsContext";
import WarningEditorPage from "@app/pages/AdminPages/warnings-admin-page/pages/WarningEditorPage";
import WarningsPage from "@app/pages/AdminPages/warnings-admin-page/WarningsPage";

import WarningViewPage from "@app/pages/complaints/pages/WarningEditorPage";
import TenantsPage from "../profile/profileCard/profileFormNav/nav/Tenants/TenantsPage";
import { EditTenantPage } from "../profile/profileCard/profileFormNav/nav/Tenants/EditTenantPage";
import { NewTenantPage } from "../profile/profileCard/profileFormNav/nav/Tenants/NewTenantPage";
import { Signature } from "../profile/profileCard/profileFormNav/nav/Signature/signature";

// Roles constantes
export const ROLES = {
  ADMIN: "Admin",
  AUTHENTICATED: "Authenticated",
  CONSEJERO: "Consejero",
  CONSTRUCTORA: "Constructora",
  CONVIVENCIA: "Convivencia",
  PUBLIC: "Public",
  RESIDENTE: "Residente",
  SERVICIO: "Servicio",
  VIGILANTE: "Vigilante",
};

export const PALMAS_DASHBOARD_PATH = "/dashboard";

// Main pages
const NewsFeed = lazy(() => import("@app/pages/news-feed-page/news-feed-page"));
const BusinessPage = lazy(
  () => import("@app/pages/bussiness-page/bussiness-page"),
);
const HousesPage = lazy(() => import("@app/pages/HousesPage/HousesPage"));
const QuestionsPage = lazy(
  () => import("@app/pages/QuestionsPage/QuestionsPage"),
);
const QuestionsAdmin = lazy(
  () => import("@app/pages/AdminPages/QuestionsAdminPage/QuestionsAdminPage"),
);
const SurveyResponse = lazy(
  () => import("@app/pages/AdminPages/SurveysPage/surveys/SurveyResponsePage"),
);
const SurveyResponseView = lazy(
  () =>
    import("@app/pages/AdminPages/SurveysPage/surveys/SurveyResponseViewPage"),
);
const SurveyEdit = lazy(
  () => import("@app/pages/AdminPages/SurveysPage/surveys/SurveyEditPage"),
);
const SurveyResponseDetail = lazy(
  () =>
    import(
      "@app/pages/AdminPages/SurveysPage/surveys/SurveyResponseDetailPage"
    ),
);

const ServerError = lazy(() => import("@app/pages/ServerErrorPage"));
const Error404 = lazy(() => import("@app/pages/Error404Page"));

// Privacy Policy page
const PrivacyPolicyPage = lazy(() => import("@app/pages/PrivacyPolicyPage"));

// Other pages
const PetsPage2 = lazy(() => import("@app/pages/pets-page"));
const Formats = lazy(() => import("@app/pages/formats-page/formats-page"));
const VehiclesPage2 = lazy(
  () => import("@app/pages/vigilance/VehiclesPage/VehiclesPage"),
);
const VisitsPage2 = lazy(
  () => import("@app/pages/vigilance/VisitsPage/VisitsPage"),
);
const LogoutFallback = lazy(() => import("./Logout"));

// Notifications pages

const NotificationsHistory = lazy(
  () => import("@app/pages/notifications-page/NotificationsHistoryPage"),
);
const NotificationsTest = lazy(
  () => import("@app/pages/test/NotificationsTestWrapper"),
);
const WebSocketTest = lazy(() => import("@app/pages/test/WebSocketTestPage"));
const SimpleWebSocketTest = lazy(
  () => import("@app/pages/test/SimpleWebSocketTest"),
);

// Chat page
const ChatPage = lazy(() => import("@app/pages/Chat/ChatPage"));

// Wrapped components with loading HOC
const AuthLayoutFallback = withLoading(AuthLayout);
const NewsFeedFallback = withLoading(NewsFeed);
const BusinessPageFallback = withLoading(BusinessPage);
const HousesFallback = withLoading(HousesPage);
const QuestionsFallback = withLoading(QuestionsPage);
const QuestionsAdminFallback = withLoading(QuestionsAdmin);
const SurveyResponseFallback = withLoading(SurveyResponse);
const SurveyResponseViewFallback = withLoading(SurveyResponseView);
const SurveyEditFallback = withLoading(SurveyEdit);
const SurveyResponseDetailFallback = withLoading(SurveyResponseDetail);

const ServerErrorFallback = withLoading(ServerError);
const Error404Fallback = withLoading(Error404);
const PrivacyPolicyFallback = withLoading(PrivacyPolicyPage);
const PetsPageFallback = withLoading(PetsPage2);
const FormatsFallback = withLoading(Formats);
const VehiclesPageFallback = withLoading(VehiclesPage2);
const VisitsPageFallback = withLoading(VisitsPage2);
const NotificationsHistoryFallback = withLoading(NotificationsHistory);
const NotificationsTestFallback = withLoading(NotificationsTest);
const WebSocketTestFallback = withLoading(WebSocketTest);
const SimpleWebSocketTestFallback = withLoading(SimpleWebSocketTest);
const PoolAccessControlFallback = withLoading(PoolAccessControlPage);
const PaymentAdminFallback = withLoading(PaymentAdminPage);
const SignatureFallback = withLoading(Signature);
const ChatPageFallback = withLoading(ChatPage);

// Profile pages
const PersonalInfo = lazy(
  () => import("@app/pages/personal-info-page/personal-info-page"),
);
const DependentInfo = lazy(
  () => import("@app/pages/dependent-info-page/dependent-info-page"),
);
const PetsInfo = lazy(() => import("@app/pages/pets-info-page/pets-info-page"));
const VehicleInfo = lazy(
  () => import("@app/pages/vehicle-info-page/vehicle-info-page"),
);

// Wrapped profile components
const PersonalInfoFallback = withLoading(PersonalInfo);
const DependentInfoFallback = withLoading(DependentInfo);
const PetsInfoFallback = withLoading(PetsInfo);
const VehicleInfoFallback = withLoading(VehicleInfo);

const AppRouter: React.FC = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/auth" element={<AuthLayoutFallback />}>
          <Route path="login" element={<LoginPage />} />
          <Route path="sign-up" element={<SignUpPage />} />
          <Route path="lock" element={<LockPage />} />
          <Route path="forgot-password" element={<ForgotPasswordPage />} />
          <Route path="security-code" element={<SecurityCodePage />} />
          <Route path="update-password" element={<NewPasswordPage />} />
          <Route
            path="privacy-policy-page"
            element={
              <ErrorBoundary>
                <PrivacyPolicyFallback />
              </ErrorBoundary>
            }
          />
        </Route>

        <Route
          path="/"
          element={
            <RequireAuth>
              <MainLayout />
            </RequireAuth>
          }
        >
          {/* Rutas generales accesibles para todos los usuarios autenticados */}
          {/* Redirigir la ruta raíz al dashboard */}
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route
            path="dashboard"
            element={
              <ErrorBoundary>
                <DashboardPage />
              </ErrorBoundary>
            }
          />
          <Route
            path="news"
            element={
              <ErrorBoundary>
                <NewsFeedFallback />
              </ErrorBoundary>
            }
          />
          <Route
            path="bussiness"
            element={
              <ErrorBoundary>
                <BusinessPageFallback />
              </ErrorBoundary>
            }
          />
          <Route
            path="houses"
            element={
              <ErrorBoundary>
                <HousesFallback />
              </ErrorBoundary>
            }
          />

          <Route
            path="notifications/history"
            element={
              <ErrorBoundary>
                <NotificationsHistoryFallback />
              </ErrorBoundary>
            }
          />
          <Route
            path="surveys"
            element={
              <ErrorBoundary>
                <QuestionsFallback />
              </ErrorBoundary>
            }
          />
          <Route
            path="surveys/:surveyId/responses"
            element={
              <ErrorBoundary>
                <SurveyResponseViewFallback />
              </ErrorBoundary>
            }
          />
          <Route
            path="surveys/response-detail/:responseId"
            element={
              <ErrorBoundary>
                <SurveyResponseDetailFallback />
              </ErrorBoundary>
            }
          />
          <Route
            path="survey/response/:surveyId"
            element={
              <ErrorBoundary>
                <SurveyResponseFallback />
              </ErrorBoundary>
            }
          />

          <Route
            path="pets"
            element={
              <ErrorBoundary>
                <PetsPageFallback />
              </ErrorBoundary>
            }
          />

          <Route
            path="documents"
            element={
              <ErrorBoundary>
                <FormatsFallback />
              </ErrorBoundary>
            }
          />
          <Route
            path="reservations"
            element={
              <ErrorBoundary>
                <ReservationRequestsPage />
              </ErrorBoundary>
            }
          />
          <Route
            path="reservations/:id"
            element={
              <ErrorBoundary>
                <ReservationRequestsDetailPage />
              </ErrorBoundary>
            }
          />
          <Route
            path="payments"
            element={
              <ErrorBoundary>
                <PaymentsPage />
              </ErrorBoundary>
            }
          />

          <Route
            path="users"
            element={
              <ErrorBoundary>
                <OperativeStaffPage />
              </ErrorBoundary>
            }
          />

          <Route
            path="complaints"
            element={
              <ErrorBoundary>
                <ComplaintsPage />
              </ErrorBoundary>
            }
          />

          <Route
            path="warnings-view/:id"
            element={
              <ErrorBoundary>
                <WarningsProvider>
                  <WarningViewPage />
                </WarningsProvider>
              </ErrorBoundary>
            }
          />

          <Route
            path="chat"
            element={
              <ErrorBoundary>
                <ChatPageFallback />
              </ErrorBoundary>
            }
          />

          <Route path="404" element={<Error404Fallback />} />
          <Route
            path="profile"
            element={
              <ErrorBoundary>
                <ProfileLayout />
              </ErrorBoundary>
            }
          >
            <Route
              path="personal-info"
              element={
                <ErrorBoundary>
                  <PersonalInfoFallback />
                </ErrorBoundary>
              }
            />
            <Route
              path="dependents"
              element={
                <ErrorBoundary>
                  <DependentInfoFallback />
                </ErrorBoundary>
              }
            />
            <Route
              path="pets"
              element={
                <ErrorBoundary>
                  <PetsInfoFallback />
                </ErrorBoundary>
              }
            />
            <Route
              path="vehicles"
              element={
                <ErrorBoundary>
                  <VehicleInfoFallback />
                </ErrorBoundary>
              }
            />

            <Route
              path="signature"
              element={
                <ErrorBoundary>
                  <SignatureFallback />
                </ErrorBoundary>
              }
            />
            <Route
              path="tenants"
              element={
                <ErrorBoundary>
                  <TenantsPage />
                </ErrorBoundary>
              }
            />
            <Route
              path="tenants/new"
              element={
                <ErrorBoundary>
                  <NewTenantPage />
                </ErrorBoundary>
              }
            />
            <Route
              path="tenants/edit/:id"
              element={
                <ErrorBoundary>
                  <EditTenantPage />
                </ErrorBoundary>
              }
            />
          </Route>

          {/* RUTAS PARA ADMINISTRADORES */}
          <Route path="admin">
            <Route
              path="surveys"
              element={
                <ErrorBoundary>
                  <QuestionsAdminFallback />
                </ErrorBoundary>
              }
            />
            <Route
              path="pool-access"
              element={
                <ErrorBoundary>
                  <PoolAccessControlFallback />
                </ErrorBoundary>
              }
            />
            <Route
              path="surveys/edit/:surveyId"
              element={
                <ErrorBoundary>
                  <SurveyEditFallback />
                </ErrorBoundary>
              }
            />
            <Route
              path="users"
              element={
                <ErrorBoundary>
                  <DataUsersTablesPage />
                </ErrorBoundary>
              }
            />
            <Route
              path="table-users"
              element={
                <ErrorBoundary>
                  <UsersAdminPage />
                </ErrorBoundary>
              }
            />
            <Route
              path="reservations"
              element={
                <ErrorBoundary>
                  <ReservationsAdminPage />
                </ErrorBoundary>
              }
            />
            <Route
              path="houses"
              element={
                <ErrorBoundary>
                  <HousesAdminPage />
                </ErrorBoundary>
              }
            />
            <Route
              path="pets"
              element={
                <ErrorBoundary>
                  <PetsAdminPage />
                </ErrorBoundary>
              }
            />
            <Route
              path="business"
              element={
                <ErrorBoundary>
                  <BussinessAdminPage />
                </ErrorBoundary>
              }
            />
            <Route
              path="budget"
              element={
                <ErrorBoundary>
                  <BudgetAdminPage />
                </ErrorBoundary>
              }
            />
            <Route
              path="payments"
              element={
                <ErrorBoundary>
                  <PaymentAdminFallback />
                </ErrorBoundary>
              }
            />

            <Route
              path="complaints"
              element={
                <ErrorBoundary>
                  <ComplaintsAdminPage />
                </ErrorBoundary>
              }
            />
            <Route
              path="warnings"
              element={
                <ErrorBoundary>
                  <WarningsProvider>
                    <WarningsPage />
                  </WarningsProvider>
                </ErrorBoundary>
              }
            />
            <Route
              path="warnings/editor/:id"
              element={
                <ErrorBoundary>
                  <WarningsProvider>
                    <WarningEditorPage />
                  </WarningsProvider>
                </ErrorBoundary>
              }
            />
            <Route
              path="vehicles"
              element={
                <ErrorBoundary>
                  <VehiclesPageFallback />
                </ErrorBoundary>
              }
            />
            <Route
              path="visits"
              element={
                <ErrorBoundary>
                  <VisitsPageFallback />
                </ErrorBoundary>
              }
            />
            <Route
              path="consents"
              element={
                <ErrorBoundary>
                  <ConsentManagementPage />
                </ErrorBoundary>
              }
            />
          </Route>
        </Route>

        {/* Rutas públicas para política de privacidad */}

        <Route
          path="server-error"
          element={
            <ErrorBoundary>
              <ServerErrorFallback />
            </ErrorBoundary>
          }
        />
        <Route path="/logout" element={<LogoutFallback />} />
        <Route path="role-test" element={<UserRoleDisplay />} />
        <Route
          path="test/notifications"
          element={<NotificationsTestFallback />}
        />
        <Route path="test/websocket" element={<WebSocketTestFallback />} />
        <Route
          path="test/websocket-simple"
          element={<SimpleWebSocketTestFallback />}
        />
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </BrowserRouter>
  );
};

export { AppRouter };
