/**
 * Hook para manejar mensajes de chat con React Query
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
} from "@tanstack/react-query";
import Http from "@app/config/http/http";
import type {
  ChatMessage,
  ChatMessagesResponse,
  CreateChatMessageRequest,
  UpdateChatMessageRequest,
} from "@app/types/chat";
import { CHAT_QUERY_KEYS } from "./useChatChannels";

/**
 * Hook para obtener mensajes de un canal con paginación infinita
 */
export const useChatMessages = (channelId: number, pageSize = 50) => {
  return useInfiniteQuery({
    queryKey: CHAT_QUERY_KEYS.messages(channelId),
    queryFn: async ({ pageParam = 1 }) => {
      const response = await Http.get<ChatMessagesResponse>(
        "/api/chat-messages",
        {
          params: {
            channelId,
            page: pageParam,
            pageSize,
          },
        },
      );
      return response.data;
    },
    enabled: !!channelId,
    getNextPageParam: (lastPage, allPages) => {
      // Verificar si hay más páginas basado en la respuesta
      const messages = lastPage?.data || [];
      const hasMore = messages.length === pageSize;
      return hasMore ? allPages.length + 1 : undefined;
    },
    initialPageParam: 1,
    staleTime: 30 * 1000, // 30 segundos
    retry: 2,
  });
};

/**
 * Hook para enviar un nuevo mensaje
 */
export const useSendChatMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (messageData: CreateChatMessageRequest) => {
      // Si hay archivos adjuntos, subirlos primero y luego crear el mensaje
      if (messageData.attachments && messageData.attachments.length > 0) {
        console.log("📎 Enviando mensaje con archivos:", messageData);

        // Paso 1: Subir archivos usando el endpoint de upload de Strapi
        const uploadedFiles = [];

        for (const file of messageData.attachments) {
          console.log(`📤 Subiendo archivo: ${file.name}`);

          const uploadFormData = new FormData();
          uploadFormData.append("files", file);

          try {
            const uploadResponse = await Http.post(
              "/api/upload",
              uploadFormData,
            );
            console.log(`✅ Archivo subido:`, uploadResponse.data);
            uploadedFiles.push(...uploadResponse.data);
          } catch (uploadError) {
            console.error(
              `❌ Error subiendo archivo ${file.name}:`,
              uploadError,
            );
            throw new Error(`Error subiendo archivo: ${file.name}`);
          }
        }

        // Paso 2: Crear mensaje con referencias a los archivos subidos
        const messageWithFiles = {
          content: messageData.content,
          channelId: messageData.channelId,
          messageType: messageData.messageType || "file",
          replyTo: messageData.replyTo,
          mentions: messageData.mentions,
          attachments: uploadedFiles.map((file) => file.id), // Solo IDs de los archivos
        };

        console.log(
          "📝 Creando mensaje con archivos subidos:",
          messageWithFiles,
        );
        const response = await Http.post("/api/chat-messages", {
          data: messageWithFiles,
        });
        return response.data;
      } else {
        // Mensaje de solo texto
        const response = await Http.post("/api/chat-messages", {
          data: messageData,
        });
        return response.data;
      }
    },
    onSuccess: (data, variables) => {
      // No agregar el mensaje aquí para evitar duplicación
      // El mensaje optimista ya fue agregado y el WebSocket enviará la versión final
      console.log("✅ Mensaje enviado exitosamente:", data?.data || data);
    },
    onError: (error) => {
      console.error("Error enviando mensaje:", error);
    },
  });
};

/**
 * Hook para editar un mensaje
 */
export const useEditChatMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      messageId,
      data,
    }: {
      messageId: number;
      data: UpdateChatMessageRequest;
    }) => {
      const response = await Http.put(`/api/chat-messages/${messageId}`, {
        data,
      });
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Actualizar el mensaje en el cache
      const updatedMessage = data.data;

      // Buscar en todas las queries de mensajes para actualizar
      queryClient.setQueriesData(
        { queryKey: ["chat", "messages"] },
        (oldData: any) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              data: page.data?.map((message: ChatMessage) =>
                message.id === variables.messageId ? updatedMessage : message,
              ),
            })),
          };
        },
      );
    },
  });
};

/**
 * Hook para eliminar un mensaje
 */
export const useDeleteChatMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (messageId: number) => {
      const response = await Http.delete(`/api/chat-messages/${messageId}`);
      return response.data;
    },
    onSuccess: (_, messageId) => {
      // Remover el mensaje del cache
      queryClient.setQueriesData(
        { queryKey: ["chat", "messages"] },
        (oldData: any) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              data: page.data?.filter(
                (message: ChatMessage) => message.id !== messageId,
              ),
            })),
          };
        },
      );
    },
  });
};

/**
 * Hook para agregar reacción a un mensaje
 */
export const useAddChatReaction = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      messageId,
      emoji,
    }: {
      messageId: number;
      emoji: string;
    }) => {
      const response = await Http.post(
        `/api/chat-messages/${messageId}/reaction`,
        {
          emoji,
        },
      );
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Actualizar las reacciones en el cache
      queryClient.setQueriesData(
        { queryKey: ["chat", "messages"] },
        (oldData: any) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              data: page.data?.map((message: ChatMessage) =>
                message.id === variables.messageId
                  ? { ...message, reactions: data.reactions }
                  : message,
              ),
            })),
          };
        },
      );
    },
  });
};

/**
 * Hook para buscar mensajes
 */
export const useSearchChatMessages = (
  query: string,
  channelId?: number,
  limit = 20,
) => {
  return useQuery({
    queryKey: ["chat", "search", query, channelId, limit],
    queryFn: async () => {
      const params: any = { query, limit };
      if (channelId) params.channelId = channelId;

      const response = await Http.get("/api/chat-messages/search", { params });
      return response.data;
    },
    enabled: query.length >= 2,
    staleTime: 60 * 1000, // 1 minuto
    retry: 1,
  });
};

/**
 * Hook para invalidar mensajes de un canal (útil para WebSocket updates)
 */
export const useInvalidateChatMessages = () => {
  const queryClient = useQueryClient();

  return {
    invalidateChannel: (channelId: number) => {
      queryClient.invalidateQueries({
        queryKey: CHAT_QUERY_KEYS.messages(channelId),
      });
    },
    invalidateAllMessages: () => {
      queryClient.invalidateQueries({ queryKey: ["chat", "messages"] });
    },
    addMessageToCache: (channelId: number, message: ChatMessage) => {
      queryClient.setQueryData(
        CHAT_QUERY_KEYS.messages(channelId),
        (oldData: any) => {
          if (!oldData) return oldData;

          const firstPage = oldData.pages[0];
          const existingMessages = firstPage?.data || [];

          // Buscar si hay un mensaje optimista que deba ser reemplazado
          const now = new Date().getTime();
          const recentThreshold = 10000; // 10 segundos

          let optimisticIndex = -1;
          let isDuplicate = false;

          for (let i = 0; i < existingMessages.length; i++) {
            const existingMsg = existingMessages[i];

            // Si tienen el mismo ID, es definitivamente duplicado
            if (existingMsg.id === message.id) {
              isDuplicate = true;
              break;
            }

            // Buscar mensaje optimista para reemplazar (ID temporal grande)
            const msgTime = new Date(existingMsg.createdAt).getTime();
            const timeDiff = Math.abs(now - msgTime);
            const isOptimistic = existingMsg.id > 1000000000000; // IDs temporales son timestamps

            if (
              isOptimistic &&
              existingMsg.content === message.content &&
              existingMsg.author.id === message.author.id &&
              timeDiff < recentThreshold
            ) {
              optimisticIndex = i;
              break;
            }

            // Verificar duplicado normal
            if (
              existingMsg.content === message.content &&
              existingMsg.author.id === message.author.id &&
              timeDiff < recentThreshold &&
              !isOptimistic
            ) {
              isDuplicate = true;
              break;
            }
          }

          // Si es duplicado, no agregar
          if (isDuplicate) {
            console.log(
              "🚫 Mensaje duplicado detectado, no agregando:",
              message,
            );
            return oldData;
          }

          // Si encontramos un mensaje optimista, reemplazarlo
          if (optimisticIndex >= 0) {
            console.log(
              "🔄 Reemplazando mensaje optimista con mensaje real:",
              message,
            );

            // Limpiar URLs temporales del mensaje optimista
            const optimisticMsg = existingMessages[optimisticIndex];
            if (optimisticMsg.attachments) {
              optimisticMsg.attachments.forEach((attachment: any) => {
                if (attachment.url && attachment.url.startsWith("blob:")) {
                  URL.revokeObjectURL(attachment.url);
                }
              });
            }

            const updatedMessages = [...existingMessages];
            updatedMessages[optimisticIndex] = message;

            return {
              ...oldData,
              pages: [
                {
                  ...firstPage,
                  data: updatedMessages,
                },
                ...oldData.pages.slice(1),
              ],
            };
          }

          return {
            ...oldData,
            pages: [
              {
                ...firstPage,
                data: [message, ...existingMessages],
              },
              ...oldData.pages.slice(1),
            ],
          };
        },
      );
    },
  };
};
