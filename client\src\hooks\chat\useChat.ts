/**
 * Hook principal para el sistema de chat con React Query
 */

import { useState, useCallback } from "react";
import { message } from "antd";
import { useChatChannels, useCreateChatChannel } from "./useChatChannels";
import {
  useChatMessages,
  useSendChatMessage,
  useEditChatMessage,
  useDeleteChatMessage,
  useAddChatReaction,
  useInvalidateChatMessages,
} from "./useChatMessages";
import { useWebSocketChat } from "../useWebSocketChat";
import { readUser } from "@app/services/localStorage.service";
import type {
  ChatChannel,
  ChatMessage,
  CreateChatMessageRequest,
} from "@app/types/chat";

export const useChat = () => {
  // Estados locales
  const [selectedChannelId, setSelectedChannelId] = useState<number | null>(
    null,
  );
  const [replyingTo, setReplyingTo] = useState<ChatMessage | null>(null);
  const [editingMessage, setEditingMessage] = useState<ChatMessage | null>(
    null,
  );

  // Usuario actual
  const currentUser = readUser();
  const currentUserId = currentUser?.id || 0;

  // React Query hooks
  const {
    data: channels = [],
    isLoading: isLoadingChannels,
    error: channelsError,
    refetch: refetchChannels,
  } = useChatChannels();

  const {
    data: messagesData,
    isLoading: isLoadingMessages,
    error: messagesError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useChatMessages(selectedChannelId || 0, 50);

  // Mutations
  const sendMessageMutation = useSendChatMessage();
  const editMessageMutation = useEditChatMessage();
  const deleteMessageMutation = useDeleteChatMessage();
  const addReactionMutation = useAddChatReaction();
  const createChannelMutation = useCreateChatChannel();

  // Utilidades para cache
  const { invalidateChannel, addMessageToCache } = useInvalidateChatMessages();

  // Canal seleccionado
  const selectedChannel =
    channels.find((channel) => channel.id === selectedChannelId) || null;

  // Mensajes del canal seleccionado
  const messages =
    messagesData?.pages?.flatMap((page) => page?.data || []) || [];

  // WebSocket para tiempo real (opcional)
  const webSocketResult = useWebSocketChat({
    onMessageReceived: (message: ChatMessage) => {
      // Agregar mensaje al cache cuando se recibe por WebSocket
      if (message.channel?.id === selectedChannelId) {
        addMessageToCache(message.channel.id, message);
      }
    },
    onMessageUpdated: (messageId: number, updates: Partial<ChatMessage>) => {
      // Invalidar cache cuando se actualiza un mensaje
      if (selectedChannelId) {
        invalidateChannel(selectedChannelId);
      }
    },
    onMessageDeleted: (messageId: number) => {
      // Invalidar cache cuando se elimina un mensaje
      if (selectedChannelId) {
        invalidateChannel(selectedChannelId);
      }
    },
  });

  // Extraer propiedades del WebSocket con valores por defecto
  const isWebSocketConnected = webSocketResult?.isConnected ?? false;
  const connectionError = webSocketResult?.connectionError ?? null;

  // Funciones de acción
  const selectChannel = useCallback((channelId: number) => {
    setSelectedChannelId(channelId);
    setReplyingTo(null);
    setEditingMessage(null);
  }, []);

  const sendMessage = useCallback(
    async (content: string, attachments?: File[]) => {
      if (
        !selectedChannelId ||
        (!content.trim() && (!attachments || attachments.length === 0))
      )
        return;

      const messageData: CreateChatMessageRequest = {
        content: content.trim(),
        channelId: selectedChannelId,
        messageType: attachments && attachments.length > 0 ? "file" : "text",
        replyTo: replyingTo?.id,
        attachments,
      };

      // Crear mensaje optimista para mostrar inmediatamente
      const optimisticMessage: ChatMessage = {
        id: Date.now(), // ID temporal
        content: messageData.content,
        messageType: messageData.messageType,
        isEdited: false,
        isDeleted: false,
        reactions: {},
        mentions: [],
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        // Para archivos adjuntos, crear una estructura temporal
        attachments:
          attachments && attachments.length > 0
            ? attachments.map((file, index) => ({
                id: Date.now() + index, // ID temporal
                name: file.name,
                url: URL.createObjectURL(file), // URL temporal para preview
                size: file.size / 1024, // Convertir a KB como Strapi
                mime: file.type,
                ext: `.${file.name.split(".").pop()}`,
                // Campos adicionales que puede necesitar Strapi
                hash: `temp_${Date.now()}_${index}`,
                provider: "temp",
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              }))
            : null,
        channel: selectedChannel
          ? {
              id: selectedChannel.id,
              name: selectedChannel.name,
              icon: selectedChannel.icon,
            }
          : undefined,
        author: {
          id: currentUserId,
          username: currentUser?.username || "Tú",
          firstName: currentUser?.firstName,
          lastName: currentUser?.lastName,
          displayName:
            currentUser?.firstName && currentUser?.lastName
              ? `${currentUser.firstName} ${currentUser.lastName}`
              : currentUser?.username || "Tú",
        },
        replyTo: replyingTo
          ? {
              id: replyingTo.id,
              content: replyingTo.content,
              author: replyingTo.author,
            }
          : undefined,
      };

      // Agregar mensaje optimista al cache inmediatamente
      console.log("📤 Agregando mensaje optimista:", optimisticMessage);
      addMessageToCache(selectedChannelId, optimisticMessage);

      try {
        const result = await sendMessageMutation.mutateAsync(messageData);
        console.log("✅ Mensaje enviado, resultado:", result);
        setReplyingTo(null);

        // El mensaje real llegará por WebSocket, pero por si acaso,
        // reemplazamos el optimista con el real después de un breve delay
        setTimeout(() => {
          if (result?.data) {
            addMessageToCache(selectedChannelId, result.data);
          }
        }, 100);
      } catch (error) {
        console.error("❌ Error enviando mensaje:", error);
        message.error("Error al enviar el mensaje. Inténtalo de nuevo.");
        // Remover mensaje optimista en caso de error
        invalidateChannel(selectedChannelId);
        throw error;
      }
    },
    [
      selectedChannelId,
      replyingTo,
      sendMessageMutation,
      selectedChannel,
      currentUserId,
      currentUser,
      addMessageToCache,
      invalidateChannel,
    ],
  );

  const editMessage = useCallback(
    async (messageId: number, content: string) => {
      try {
        await editMessageMutation.mutateAsync({
          messageId,
          data: { content: content.trim() },
        });
        setEditingMessage(null);
        message.success("Mensaje editado correctamente");
      } catch (error) {
        console.error("Error editando mensaje:", error);
        message.error("Error al editar el mensaje");
        throw error;
      }
    },
    [editMessageMutation],
  );

  const deleteMessage = useCallback(
    async (messageId: number) => {
      try {
        await deleteMessageMutation.mutateAsync(messageId);
        message.success("Mensaje eliminado");
      } catch (error) {
        console.error("Error eliminando mensaje:", error);
        message.error("Error al eliminar el mensaje");
        throw error;
      }
    },
    [deleteMessageMutation],
  );

  const addReaction = useCallback(
    async (messageId: number, emoji: string) => {
      try {
        await addReactionMutation.mutateAsync({ messageId, emoji });
      } catch (error) {
        console.error("Error agregando reacción:", error);
        message.error("Error al agregar reacción");
        throw error;
      }
    },
    [addReactionMutation],
  );

  const createChannel = useCallback(
    async (channelData: Partial<ChatChannel>) => {
      try {
        const result = await createChannelMutation.mutateAsync(channelData);
        return result;
      } catch (error) {
        console.error("Error creando canal:", error);
        throw error;
      }
    },
    [createChannelMutation],
  );

  const loadMoreMessages = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Estados combinados
  const isLoading = isLoadingChannels || isLoadingMessages;
  const error = channelsError || messagesError;
  const isSending = sendMessageMutation.isPending;
  const isEditing = editMessageMutation.isPending;
  const isDeleting = deleteMessageMutation.isPending;

  return {
    // Datos
    channels,
    selectedChannel,
    messages,
    currentUserId,

    // Estados
    isLoading,
    isLoadingChannels,
    isLoadingMessages,
    error,
    isSending,
    isEditing,
    isDeleting,

    // WebSocket
    isWebSocketConnected,
    connectionError,

    // Estados de UI
    replyingTo,
    editingMessage,
    selectedChannelId,

    // Paginación
    hasNextPage,
    isFetchingNextPage,

    // Acciones
    selectChannel,
    sendMessage,
    editMessage,
    deleteMessage,
    addReaction,
    createChannel,
    loadMoreMessages,
    refetchChannels,

    // Setters para UI
    setReplyingTo,
    setEditingMessage,
  };
};
