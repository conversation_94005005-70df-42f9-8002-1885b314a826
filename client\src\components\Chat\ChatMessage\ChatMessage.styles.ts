/**
 * Estilos para el componente ChatMessage
 */

import styled, { css } from "styled-components";

interface MessageContainerProps {
  $isOwnMessage: boolean;
  $compact: boolean;
}

interface MessageContentProps {
  $isOwnMessage: boolean;
  $hasAvatar: boolean;
}

interface MessageTextProps {
  $isDeleted: boolean;
}

interface MessageActionsProps {
  $isOwnMessage: boolean;
}

export const MessageContainer = styled.div<MessageContainerProps>`
  display: flex;
  gap: 8px;
  padding: ${({ $compact }) => ($compact ? "2px 16px" : "8px 16px")};
  position: relative;

  ${({ $isOwnMessage }) =>
    $isOwnMessage &&
    css`
      flex-direction: row-reverse;
    `}

  &:hover {
    background-color: ${({ theme }) =>
      theme.background === "#ffffff" ? "#f5f5f5" : "#1f1f1f"};
  }

  /* Indicador sutil de que hay acciones disponibles */
  &::before {
    content: "";
    position: absolute;
    ${({ $isOwnMessage }) => ($isOwnMessage ? "left: 8px;" : "right: 8px;")}
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background-color: ${({ theme }) =>
      theme.background === "#ffffff" ? "#d9d9d9" : "#434343"};
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover::before {
    opacity: 0.6;
  }

  transition: background-color 0.2s ease;
`;

export const AvatarContainer = styled.div`
  flex-shrink: 0;
  margin-top: 4px;
`;

export const MessageContent = styled.div<MessageContentProps>`
  flex: 1;
  min-width: 0;

  ${({ $isOwnMessage }) =>
    $isOwnMessage &&
    css`
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    `}

  ${({ $hasAvatar, $isOwnMessage }) =>
    !$hasAvatar &&
    !$isOwnMessage &&
    css`
      margin-left: 40px;
    `}
`;

export const MessageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  gap: 8px;
`;

export const ReplyContainer = styled.div`
  background-color: ${({ theme }) =>
    theme.background === "#ffffff" ? "#f0f0f0" : "#2a2a2a"};
  border-left: 3px solid ${({ theme }) => theme.primary};
  padding: 4px 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  font-size: 12px;

  > * {
    display: block;
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
`;

export const MessageText = styled.div<MessageTextProps>`
  word-wrap: break-word;
  word-break: break-word;
  line-height: 1.4;

  ${({ $isDeleted }) =>
    $isDeleted &&
    css`
      opacity: 0.6;
      font-style: italic;
    `}

  /* Estilos para menciones */
  .mention {
    background-color: ${({ theme }) => theme.primary}20;
    color: ${({ theme }) => theme.primary};
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 500;
  }

  /* Estilos para enlaces */
  a {
    color: ${({ theme }) => theme.primary};
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  /* Estilos para código inline */
  code {
    background-color: ${({ theme }) =>
      theme.background === "#ffffff" ? "#f5f5f5" : "#2a2a2a"};
    padding: 2px 4px;
    border-radius: 3px;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 0.9em;
  }

  /* Estilos para bloques de código */
  pre {
    background-color: ${({ theme }) =>
      theme.background === "#ffffff" ? "#f5f5f5" : "#2a2a2a"};
    padding: 8px 12px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 4px 0;

    code {
      background: none;
      padding: 0;
    }
  }
`;

export const MessageActions = styled.div<MessageActionsProps>`
  position: absolute;
  top: -12px;
  ${({ $isOwnMessage }) => ($isOwnMessage ? "left: -80px;" : "right: -80px;")}
  background-color: ${({ theme }) =>
    theme.background === "#ffffff" ? "#ffffff" : "#1f1f1f"};
  border: 1px solid
    ${({ theme }) => (theme.background === "#ffffff" ? "#d9d9d9" : "#434343")};
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
  opacity: 0;
  transform: translateY(4px);
  transition: all 0.2s ease;
  pointer-events: none;

  /* Mostrar cuando el contenedor padre está en hover */
  ${MessageContainer}:hover & {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }

  .ant-btn {
    border: none;
    box-shadow: none;
    background-color: transparent;
    color: ${({ theme }) =>
      theme.background === "#ffffff" ? "#595959" : "#bfbfbf"};

    &:hover {
      background-color: ${({ theme }) =>
        theme.background === "#ffffff" ? "#f5f5f5" : "#2a2a2a"};
      color: ${({ theme }) =>
        theme.background === "#ffffff" ? "#262626" : "#ffffff"};
    }

    &:focus {
      background-color: ${({ theme }) =>
        theme.background === "#ffffff" ? "#e6f7ff" : "#111b26"};
      color: ${({ theme }) => theme.primary};
    }
  }

  /* Flecha indicadora */
  &::after {
    content: "";
    position: absolute;
    ${({ $isOwnMessage }) => ($isOwnMessage ? "right: 12px;" : "left: 12px;")}
    bottom: -6px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid
      ${({ theme }) => (theme.background === "#ffffff" ? "#ffffff" : "#1f1f1f")};
  }
`;

export const SystemMessage = styled.div`
  display: flex;
  justify-content: center;
  padding: 8px 16px;
  margin: 4px 0;

  > * {
    background-color: ${({ theme }) =>
      theme.background === "#ffffff" ? "#f0f0f0" : "#2a2a2a"};
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
  }
`;

// Estilos para las reacciones
export const ReactionsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
`;

export const ReactionButton = styled.button<{ $isActive: boolean }>`
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 2px 6px;
  border: 1px solid
    ${({ theme }) => (theme.background === "#ffffff" ? "#d9d9d9" : "#434343")};
  border-radius: 12px;
  background-color: ${({ $isActive, theme }) =>
    $isActive
      ? theme.primary + "20"
      : theme.background === "#ffffff"
        ? "#ffffff"
        : "#1f1f1f"};
  color: ${({ theme }) => theme.textMain};
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.primary + "10"};
    border-color: ${({ theme }) => theme.primary};
  }

  .emoji {
    font-size: 14px;
  }

  .count {
    font-weight: 500;
    min-width: 12px;
    text-align: center;
  }
`;

export const AddReactionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: 1px dashed
    ${({ theme }) => (theme.background === "#ffffff" ? "#d9d9d9" : "#434343")};
  border-radius: 12px;
  background-color: transparent;
  color: ${({ theme }) => theme.textMain};
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.6;

  &:hover {
    opacity: 1;
    border-color: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.primary};
  }
`;

// Estilos para el picker de emojis
export const EmojiPickerContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  padding: 8px;
  max-width: 240px;
`;

export const EmojiButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background-color: transparent;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${({ theme }) =>
      theme.background === "#ffffff" ? "#f5f5f5" : "#2a2a2a"};
  }
`;
