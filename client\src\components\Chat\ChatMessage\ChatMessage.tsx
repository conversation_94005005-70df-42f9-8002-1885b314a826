/**
 * Componente para mostrar un mensaje individual del chat
 */

import React, { useState } from "react";
import {
  Avatar,
  Button,
  Dropdown,
  Tooltip,
  Typography,
  Space,
  Popover,
  Tag,
  MenuProps,
} from "antd";
import {
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  MessageOutlined,
  SmileOutlined,
  CopyOutlined,
} from "@ant-design/icons";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import type { ChatMessageProps } from "@app/types/chat";
import { MessageReactions } from "./MessageReactions";
import { EmojiPicker } from "./EmojiPicker";
import { ChatAttachments } from "./ChatAttachments";
import * as S from "./ChatMessage.styles";

const { Text, Paragraph } = Typography;

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  currentUserId,
  onReply,
  onEdit,
  onDelete,
  onReaction,
  showAvatar = true,
  compact = false,
}) => {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showActions, setShowActions] = useState(false);

  const isOwnMessage = message.author.id === currentUserId;
  const isDeleted = message.isDeleted;
  const isSystemMessage = message.messageType === "system";

  // Formatear tiempo
  const timeAgo = formatDistanceToNow(new Date(message.createdAt), {
    addSuffix: true,
    locale: es,
  });

  // Nombre a mostrar
  const displayName =
    message.author.displayName ||
    (message.author.firstName && message.author.lastName
      ? `${message.author.firstName} ${message.author.lastName}`
      : message.author.username);

  // Color del avatar basado en el ID del usuario
  const avatarColors = ["#f56a00", "#7265e6", "#ffbf00", "#00a2ae", "#87d068"];
  const avatarColor = avatarColors[message.author.id % avatarColors.length];

  // Menú de acciones
  const actionItems: MenuProps["items"] = [
    {
      key: "reply",
      icon: <MessageOutlined />,
      label: "Responder",
      onClick: () => onReply(message),
    },
    {
      key: "copy",
      icon: <CopyOutlined />,
      label: "Copiar texto",
      onClick: () => {
        navigator.clipboard.writeText(message.content);
      },
    },
    ...(isOwnMessage && !isDeleted
      ? [
          {
            key: "edit",
            icon: <EditOutlined />,
            label: "Editar",
            onClick: () => onEdit(message),
          },
          {
            key: "delete",
            icon: <DeleteOutlined />,
            label: "Eliminar",
            danger: true,
            onClick: () => onDelete(message.id),
          },
        ]
      : []),
  ];

  // Manejar selección de emoji
  const handleEmojiSelect = (emoji: string) => {
    onReaction(message.id, emoji);
    setShowEmojiPicker(false);
  };

  // Renderizar mensaje del sistema
  if (isSystemMessage) {
    return (
      <S.SystemMessage>
        <Text type="secondary" italic>
          {message.content}
        </Text>
      </S.SystemMessage>
    );
  }

  return (
    <S.MessageContainer
      $isOwnMessage={isOwnMessage}
      $compact={compact}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Avatar */}
      {showAvatar && !isOwnMessage && (
        <S.AvatarContainer>
          <Avatar
            size={compact ? 24 : 32}
            style={{ backgroundColor: avatarColor }}
          >
            {displayName.charAt(0).toUpperCase()}
          </Avatar>
        </S.AvatarContainer>
      )}

      {/* Contenido del mensaje */}
      <S.MessageContent
        $isOwnMessage={isOwnMessage}
        $hasAvatar={showAvatar && !isOwnMessage}
      >
        {/* Header con nombre y tiempo */}
        {!compact && (
          <S.MessageHeader>
            <Space size="small">
              <Text strong style={{ color: avatarColor }}>
                {displayName}
              </Text>
              {message.isEdited && <Tag color="default">editado</Tag>}
            </Space>
            <Tooltip title={new Date(message.createdAt).toLocaleString()}>
              <Text type="secondary" style={{ fontSize: "12px" }}>
                {timeAgo}
              </Text>
            </Tooltip>
          </S.MessageHeader>
        )}

        {/* Mensaje al que responde */}
        {message.replyTo && (
          <S.ReplyContainer>
            <Text type="secondary" style={{ fontSize: "12px" }}>
              <MessageOutlined /> Respondiendo a{" "}
              {message.replyTo.author.username}:
            </Text>
            <Text type="secondary" style={{ fontSize: "12px" }} ellipsis>
              {message.replyTo.content}
            </Text>
          </S.ReplyContainer>
        )}

        {/* Contenido del mensaje */}
        <S.MessageText $isDeleted={isDeleted}>
          {isDeleted ? (
            <Text type="secondary" italic>
              {message.content}
            </Text>
          ) : (
            <Paragraph style={{ margin: 0, whiteSpace: "pre-wrap" }}>
              {message.content}
            </Paragraph>
          )}
        </S.MessageText>

        {/* Archivos adjuntos */}
        {message.attachments && message.attachments.length > 0 && (
          <ChatAttachments attachments={message.attachments} />
        )}

        {/* Reacciones */}
        {Object.keys(message.reactions || {}).length > 0 && (
          <MessageReactions
            reactions={message.reactions}
            currentUserId={currentUserId}
            onReactionClick={handleEmojiSelect}
            onReactionAdd={() => setShowEmojiPicker(true)}
          />
        )}

        {/* Acciones del mensaje */}
        <S.MessageActions $isOwnMessage={isOwnMessage}>
          <Space size="small">
            {/* Botón de emoji */}
            <Popover
              content={
                <EmojiPicker
                  onEmojiSelect={handleEmojiSelect}
                  visible={true}
                  onVisibleChange={setShowEmojiPicker}
                />
              }
              trigger="click"
              open={showEmojiPicker}
              onOpenChange={setShowEmojiPicker}
              placement="topLeft"
              styles={{ body: { padding: 0 } }}
            >
              <Tooltip title="Agregar reacción">
                <Button type="text" size="small" icon={<SmileOutlined />} />
              </Tooltip>
            </Popover>

            {/* Menú de acciones */}
            <Dropdown
              menu={{ items: actionItems }}
              trigger={["click"]}
              placement={isOwnMessage ? "bottomLeft" : "bottomRight"}
            >
              <Tooltip title="Más opciones">
                <Button type="text" size="small" icon={<MoreOutlined />} />
              </Tooltip>
            </Dropdown>
          </Space>
        </S.MessageActions>
      </S.MessageContent>
    </S.MessageContainer>
  );
};
